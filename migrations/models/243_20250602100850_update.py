from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "session" ADD "user_identifier" VARCHAR(200);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "session" DROP COLUMN "user_identifier";
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""
