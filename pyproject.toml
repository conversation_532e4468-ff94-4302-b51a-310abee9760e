[tool.poetry]
name = "mygpt"
version = "0.1.0"
description = ""
authors = ["ren.chen <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10.11,<3.11"
pydantic = "1.10.9"
uvicorn = "0.27.0"
fastapi = "^0.110.0"
fastapi-auth0 = "^0.3.2"
nest-asyncio = "^1.5.6"
itsdangerous = "^2.1.2"
loader = "^2017.9.11"
tortoise-orm = "^0.19.3"
asyncpg = "^0.27.0"
python-docx = "^0.8.11"
python-multipart = "^0.0.5"
fastapi-pagination = "^0.11.4"
pycryptodome = "^3.17"
aioboto3 = "^12.3.0"
aiobotocore = "^2.4.2"
types-aiobotocore-s3 = "^2.4.2"
auth0-python = "^4.0.0"
asyncio = "^3.4.3"
aerich = "0.7.1"
pinecone-client = "^2.2.1"
pexpect = "^4.8.0"
sqlalchemy = "2.0.12"
celery = { extras = ["redis"], version = "^5.4.0" }
fake-useragent = "^1.1.3"
stripe = "^5.4.0"
python-dotenv = "^1.0.0"
opensearch-py = { version = "^2.2.0", extras = ["async"] }
openpyxl = "3.1.5"
qdrant-client = ">=1.10.0,<1.11.0" # 1.10.*
tiktoken = "0.6.0"
sentry-sdk = { extras = ["fastapi"], version = "^1.28.1" }
cssutils = "^2.7.1"
readability-lxml = "^0.8.1"
cohere = "^4.21"
black = "^23.7.0"
sparticleinc-skywalking = "^1.0.2"
cryptography = "^41.0.4"
scipy = "^1.11.3"
websockets = "^12.0"
sortedcontainers = "^2.4.0"
langchain = "^0.1.6"
openai = "1.11.0"
httpx = "^0.25.1"
pandas = "^2.1.3"
arize-phoenix = "^1.9.0"
pypdf = "^4.3.1"
py3langid = "^0.2.2"
pip = "^24.0"
google-api-python-client = "^2.119.0"
pytest-asyncio = "^0.23.5"
dateparser = "^1.2.0"
llama-index = "^0.10.17"
redis = "5.2.0"
sse-starlette = "^2.0.0"
xlrd = "2.0.1"
email-validator = "^2.2.0"
lark-oapi = "^1.3.4"
transformers = "^4.45.2"
flower = "^2.0.1"
colorama = "^0.4.6"
Agently = "^*******"
lingua-language-detector = "^2.0.2"
lunar-python = "^1.3.12"
grpcio = "^1.70.0"
grpcio-reflection = "1.59.0"
aioredis = "^2.0.1"
zstandard = "^0.23.0"
ijson = "^3.3.0"
aiofiles = "^24.1.0"
nltk = "^3.9.1"
requests = "^2.32.3"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.0.1"
pre-commit = "^3.6.0"
tortoise-orm-stubs = "^1.0.2"


[tool.poetry.group.test.dependencies]
# chromadb = "^0.4.22"
matplotlib = "^3.8.2"
llama-index-llms-anthropic = "^0.1.4"

[tool.aerich]
tortoise_orm = "mygpt.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
target-version = ['py310']


#[[tool.poetry.source]]
#name = "pypi-th"
#priority = "primary"
#url = "https://pypi.tuna.tsinghua.edu.cn/simple"
