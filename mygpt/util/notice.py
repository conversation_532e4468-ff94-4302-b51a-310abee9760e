import os
import re
import time
import asyncio
import aiohttp
import urllib.parse
import requests
import json
import base64
from typing import Dict, List, Tu<PERSON>, Pattern
from loguru import logger as logging

try:
    from Cryptodome.Cipher import AES
    from Cryptodome.Util.Padding import pad, unpad
except ImportError:
    try:
        from Crypto.Cipher import AES
        from Crypto.Util.Padding import pad, unpad
    except ImportError:
        logging.error("请安装 pycryptodome 或 pycrypto 库以支持电话报警功能")

from mygpt.send_email import send_admin_alert_email
from mygpt.settings import RedisClient

# 错误类型常量
ERROR_TYPE_AI_API = "error_ai_api"  # API 503错误，通常是API账号异常
ERROR_TYPE_VECTOR = "error_vector"  # 向量数据库连接错误
ERROR_TYPE_GENERAL = "error_general"  # 业务错误，不能恢复

# 错误类型匹配规则 - 使用正则表达式增强匹配能力
# 增加大模型相关错误模式
ERROR_TYPE_LLM = "error_llm"  # 大模型相关错误

# 电话报警相关常量
PHONE_CALL_API_URL = os.getenv('PHONE_CALL_API_URL', '')  # 电话呼叫API地址
PHONE_CALL_ENCRYPTION_KEY = os.getenv('PHONE_CALL_ENCRYPTION_KEY', '')  # 加密密钥
PHONE_CALL_RECIPIENTS = os.getenv('PHONE_CALL_RECIPIENTS', '')  # 接收电话号码列表，以逗号分隔

# 默认错误模式定义
_DEFAULT_ERROR_PATTERNS: Dict[str, List[str]] = {
    ERROR_TYPE_AI_API: [
        r"Error code: *(401|503|429|529)",  # 各种API错误码
        r"[Ss]ervice [Uu]navailable",      # 服务不可用
        r"[Rr]ate limit exceeded",         # 超过限流
        r"API quota exceeded",             # 超过配额
        r"too many requests",              # 请求过多
        r"capacity has been reached",      # 达到容量上限
        r"OpenAI API (error|issue)",       # OpenAI API错误
    ],
    ERROR_TYPE_VECTOR: [
        r"[Cc]onnection .{0,20} failed",   # 连接失败
        r"[Cc]onnect timeout",              # 连接超时
        r"[Dd]atabase .{0,20} error",      # 数据库错误
        r"[Vv]ector .{0,20} error",        # 向量相关错误
    ],
    ERROR_TYPE_LLM: [
        r"'NoneType' object has no attribute 'get'",  # 大模型未返回数据
        r"model=(gpt-4|gpt-3|claude).{0,50}error",   # 大模型相关错误
        r"response format (error|incorrect)",          # 响应格式错误
    ],
    # 确保GENERAL类型存在，作为默认匹配
    ERROR_TYPE_GENERAL: [
        r".*"  # 匹配任何内容，作为兼容保障
    ]
}

# 安全地编译正则表达式
_ERROR_PATTERNS = _DEFAULT_ERROR_PATTERNS.copy()

# 使用安全的编译方式
ERROR_TYPE_PATTERNS: Dict[str, List[Pattern]] = {}

try:
    # 逐个编译模式，确保一个模式编译错误不影响其他模式
    for error_type, patterns in _ERROR_PATTERNS.items():
        ERROR_TYPE_PATTERNS[error_type] = []
        for pattern in patterns:
            try:
                compiled_pattern = re.compile(pattern)
                ERROR_TYPE_PATTERNS[error_type].append(compiled_pattern)
            except re.error as e:
                logging.error(f"正则表达式编译错误: 类型={error_type}, 模式='{pattern}', 错误={str(e)}")
                # 跳过错误模式继续编译其他模式
                continue
    
    # 确保每种错误类型至少有一个模式
    for error_type in _ERROR_PATTERNS.keys():
        if not ERROR_TYPE_PATTERNS.get(error_type) or len(ERROR_TYPE_PATTERNS[error_type]) == 0:
            logging.warning(f"错误类型 {error_type} 没有有效的模式，使用默认模式")
            # 使用通配符模式作为备选
            ERROR_TYPE_PATTERNS[error_type] = [re.compile(r".*")]
            
    # 确保 GENERAL 类型一定存在
    if ERROR_TYPE_GENERAL not in ERROR_TYPE_PATTERNS or not ERROR_TYPE_PATTERNS[ERROR_TYPE_GENERAL]:
        ERROR_TYPE_PATTERNS[ERROR_TYPE_GENERAL] = [re.compile(r".*")]
        
except Exception as e:
    # 出现意外错误时，确保基本功能不受影响
    logging.error(f"编译错误模式时出现异常: {str(e)}", exc_info=True)
    # 使用简单的默认模式
    ERROR_TYPE_PATTERNS = {
        ERROR_TYPE_AI_API: [re.compile(r"API"), re.compile(r"rate limit")],
        ERROR_TYPE_VECTOR: [re.compile(r"connect"), re.compile(r"database")],
        ERROR_TYPE_LLM: [re.compile(r"NoneType"), re.compile(r"model=")],
        ERROR_TYPE_GENERAL: [re.compile(r".*")]
    }

# 从环境变量获取NOTICE_AI_API_CONFIG作为默认配置 (count, window(秒), cooldown(秒))
AI_API_CONFIG = os.environ.get("NOTICE_AI_API_CONFIG", "3,30,300")

# 电话呼叫相关函数
def encrypt_data(data, encryption_key):
    """加密数据"""
    if not encryption_key:
        logging.error("未设置加密密钥，无法加密数据")
        return None
    try:
        cipher = AES.new(encryption_key.encode(), AES.MODE_CBC)
        ct_bytes = cipher.encrypt(pad(json.dumps(data).encode(), AES.block_size))
        iv = base64.b64encode(cipher.iv).decode('utf-8')
        ct = base64.b64encode(ct_bytes).decode('utf-8')
        return {'iv': iv, 'data': ct}
    except Exception as e:
        logging.error(f"加密数据失败: {str(e)}")
        return None

def decrypt_data(encrypted_data, encryption_key):
    """解密数据"""
    if not encryption_key or not encrypted_data:
        return None
    try:
        iv = base64.b64decode(encrypted_data['iv'])
        ct = base64.b64decode(encrypted_data['data'])
        cipher = AES.new(encryption_key.encode(), AES.MODE_CBC, iv)
        pt = unpad(cipher.decrypt(ct), AES.block_size)
        return json.loads(pt.decode('utf-8'))
    except Exception as e:
        logging.error(f"解密失败: {str(e)}")
        return None

def make_call(to_phone, message, api_url, encryption_key):
    """发起电话呼叫"""
    if not api_url or not encryption_key:
        logging.error("未配置电话呼叫API或加密密钥，无法发起电话呼叫")
        return False
    
    # 准备请求数据
    data = {
        "to": to_phone,
        "message": message
    }
    
    # 加密数据
    encrypted_data = encrypt_data(data, encryption_key)
    if not encrypted_data:
        return False
    
    try:
        # 发送请求
        response = requests.post(api_url, json=encrypted_data, timeout=10)
        
        # 解密响应
        if response.status_code == 200:
            decrypted_response = decrypt_data(response.json(), encryption_key)
            if decrypted_response:
                logging.info(f"向 {to_phone} 发起电话呼叫成功: {decrypted_response}")
                return True
            else:
                logging.error(f"向 {to_phone} 发起电话呼叫响应解密失败")
        else:
            logging.error(f"向 {to_phone} 发起电话呼叫失败: {response.status_code} {response.text}")
            
    except Exception as e:
        logging.error(f"向 {to_phone} 发起电话呼叫请求失败: {str(e)}")
    
    return False
try:
    DEFAULT_THRESHOLD = tuple(map(int, AI_API_CONFIG.strip().split(",")))
    if len(DEFAULT_THRESHOLD) != 3:
        logging.warning(f"NOTICE_AI_API_CONFIG格式错误: {AI_API_CONFIG}，使用备用配置(3,30,300)")
        DEFAULT_THRESHOLD = (3, 30, 300)
except (ValueError, AttributeError):
    logging.warning(f"NOTICE_AI_API_CONFIG解析错误: {AI_API_CONFIG}，使用备用配置(3,30,300)")
    DEFAULT_THRESHOLD = (3, 30, 300)


def _parse_threshold_config(config_str: str) -> Tuple[int, int, int]:
    """解析阈值配置字符串，格式：'count,window,cooldown'"""
    try:
        count, window, cooldown = map(int, config_str.strip().split(","))
        return count, window, cooldown
    except (ValueError, AttributeError):
        return DEFAULT_THRESHOLD


# 从环境变量读取每种错误类型的配置
ERROR_CONFIGS: Dict[str, Tuple[int, int, int]] = {
    ERROR_TYPE_AI_API: _parse_threshold_config(
        os.environ.get("NOTICE_AI_API_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_VECTOR: _parse_threshold_config(
        os.environ.get("NOTICE_VECTOR_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_LLM: _parse_threshold_config(
        os.environ.get("NOTICE_LLM_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_GENERAL: DEFAULT_THRESHOLD,
}


def _get_redis_key(error_type: str, field: str) -> str:
    """生成Redis键名"""
    return f"error_notice:{error_type}:{field}"


def _get_error_type(error_msg: str) -> str:
    """使用正则表达式匹配错误消息，确定错误类型"""
    # 确保是字符串
    error_msg = str(error_msg)
    
    # 使用正则表达式匹配
    for error_type, patterns in ERROR_TYPE_PATTERNS.items():
        if any(pattern.search(error_msg) for pattern in patterns):
            return error_type
            
    # 默认为一般错误
    return ERROR_TYPE_GENERAL


async def notice(e: str):
    notice_list = os.environ.get("EMAIL_ADMIN_OUTGOING_NOTICE_LIST")
    if not notice_list:
        logging.error("EMAIL_ADMIN_OUTGOING_NOTICE_LIST 环境变量未设置")
        return

    try:
        error_msg = str(e)
        logging.warning(f"⚠️ 错误通知 | 收到错误消息: {error_msg}")

        # 使用新的错误类型判断逻辑
        error_type = _get_error_type(error_msg)
        logging.warning(f"⚠️ 错误通知 | 错误类型: {error_type}")

        current_time = int(time.time())
        threshold_count, time_window, cooldown = ERROR_CONFIGS[error_type]
        logging.warning(
            f"⚠️ 错误通知 | 配置 - 错误阈值: {threshold_count}次, 时间窗口: {time_window}秒, 冷却时间: {cooldown}秒"
        )

        redis_client = RedisClient.get_client()

        # 获取Redis中的计数器状态
        count_key = _get_redis_key(error_type, "count")
        last_sent_key = _get_redis_key(error_type, "last_sent")
        first_error_key = _get_redis_key(error_type, "first_error")

        # 获取当前状态
        last_sent = int(await redis_client.get(last_sent_key) or 0)
        first_error = int(await redis_client.get(first_error_key) or 0)

        # 转换时间戳为人类可读格式
        last_sent_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_sent)) if last_sent > 0 else "从未发送"
        first_error_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(first_error)) if first_error > 0 else "无首次错误"
        logging.warning(f"⚠️ 错误通知 | 历史记录 - 上次发送时间: {last_sent_time}, 首次错误时间: {first_error_time}")

        # 检查是否需要开始新的错误计数周期
        if (
            current_time - last_sent > cooldown
            and current_time - first_error > time_window  # 超过冷却时间
        ):  # 超过时间窗口
            # 只有同时满足冷却时间和时间窗口条件才重置计数
            await redis_client.set(count_key, 1)
            await redis_client.set(first_error_key, current_time)
            current_count = 1
            logging.warning("⚠️ 错误通知 | ▶️ 开始新的错误计数周期 - 已重置计数器")
        else:
            # 在时间窗口内继续累加计数
            current_count = int(await redis_client.incr(count_key))
            if current_count == 1:  # 如果是第一次错误，记录时间
                await redis_client.set(first_error_key, current_time)

        # 将时间差转换为更友好的格式
        def format_time_diff(seconds):
            if seconds < 60:
                return f"{seconds}秒"
            elif seconds < 3600:
                minutes = seconds // 60
                remain_seconds = seconds % 60
                if remain_seconds == 0:
                    return f"{minutes}分钟"
                else:
                    return f"{minutes}分钟{remain_seconds}秒"
            elif seconds < 86400:
                hours = seconds // 3600
                minutes = (seconds % 3600) // 60
                if minutes == 0:
                    return f"{hours}小时"
                else:
                    return f"{hours}小时{minutes}分钟"
            else:
                days = seconds // 86400
                hours = (seconds % 86400) // 3600
                if hours == 0:
                    return f"{days}天"
                else:
                    return f"{days}天{hours}小时"
                
        # 转换时间戳为人类可读格式
        current_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))
        first_error_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(first_error)) if first_error > 0 else "无首次错误"
        
        # 添加距离上次发送的时间信息
        last_sent_diff = ""
        if last_sent > 0:
            time_since_last = format_time_diff(current_time - last_sent)
            last_sent_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_sent))
            last_sent_diff = f", 上次发送: {last_sent_str} (距今 {time_since_last})"
        
        logging.warning(
            f"⚠️ 错误通知 | 计数状态 - 当前错误计数: {current_count}/{threshold_count}, 首次错误: {first_error_time_str}, 当前时间: {current_time_str}{last_sent_diff}"
        )

        # 检查是否需要发送邮件
        should_send = False
        if current_count >= threshold_count:  # 错误计数达到阈值
            if last_sent == 0:  # 从未发送过通知
                should_send = True
            elif current_time - last_sent > cooldown:  # 超过冷却时间
                should_send = True

        # 简化决策日志逻辑
        count_check = f"错误计数: {current_count}/{threshold_count} {'✓' if current_count >= threshold_count else '✗'}"
        
        if not should_send:
            # 不发送通知的情况
            if current_count < threshold_count:
                decision_reason = f"原因: 错误计数不足 ({current_count}/{threshold_count})"
            else:  # current_count >= threshold_count
                if last_sent > 0 and current_time - last_sent <= cooldown:
                    time_diff = current_time - last_sent  # 已经过去的时间
                    remain_time = cooldown - time_diff   # 还需等待的时间
                    formatted_diff = format_time_diff(time_diff)
                    formatted_remain = format_time_diff(remain_time)
                    decision_reason = f"原因: 还在冷却期内 (已过: {formatted_diff}, 还需等待: {formatted_remain})"
                else:
                    # 这种情况不应该发生，但为了完整性添加
                    decision_reason = "原因: 逻辑异常、请检查代码"
        else:
            # 发送通知的情况
            if last_sent == 0:
                decision_reason = f"原因: 错误计数足够 ({current_count}/{threshold_count}) 且为首次发送"
            else:
                time_diff = current_time - last_sent
                formatted_diff = format_time_diff(time_diff)
                formatted_cooldown = format_time_diff(cooldown)
                decision_reason = f"原因: 错误计数足够 ({current_count}/{threshold_count}) 且冷却时间已过 (已过: {formatted_diff}, 冷却时间: {formatted_cooldown})"
        
        logging.warning(
            f"⚠️ 错误通知 | 发送决策 - 是否发送: {'✓' if should_send else '✗'}, {decision_reason}"
        )

        if should_send:
            error_type_display = {
                ERROR_TYPE_AI_API: "API 503 429等 错误 (API账号/限流问题)",
                ERROR_TYPE_VECTOR: "向量数据库连接错误",
                ERROR_TYPE_LLM: "大模型相关错误",
                ERROR_TYPE_GENERAL: "通用系统错误",
            }.get(error_type, "未分类错误")

            # 格式化错误信息
            error_details = (
                f"错误类型: {error_type_display}\n"
                f"错误次数: {current_count} (阈值: {threshold_count})\n"
                f"时间窗口: {time_window}秒\n"
                f"冷却时间: {cooldown}秒\n"
                f"原始错误: {error_msg}"
            )

            logging.warning(f"⚠️ 错误通知 | 📧 准备发送邮件到: {notice_list}")
            try:
                send_admin_alert_email(
                    to_email=notice_list,
                    title=f"错误报警: {error_type_display}",
                    cause=error_details,
                )
            except Exception as e:
                logging.error(f"发送邮件失败: {str(e)}", exc_info=True)
                pass
            logging.warning("⚠️ 错误通知 | ✅ 邮件发送完成")

            # 发送Bark通知
            try:
                bark_ids_str = os.environ.get("BARK_API_ID")
                if bark_ids_str:
                    # 构造Bark通知内容
                    bark_main_title = "线上事故持续响铃"  # 主标题，固定为"线上事故持续响铃"
                    bark_subtitle = error_type_display  # 副标题，使用错误类型
                    
                    # 处理超长错误消息，保留前100字和后100字
                    if len(error_msg) > 1000:
                        bark_body = f"{error_msg[:100]}...（省略{len(error_msg)-200}字）...{error_msg[-100:]}"
                    else:
                        bark_body = error_msg
                    
                    # 处理多个Bark ID（以逗号分隔）
                    # 如果bark id包含冒号(:)，取冒号后面的部分
                    bark_ids = []
                    for bid in bark_ids_str.split(","):
                        bid = bid.strip()
                        if bid:
                            if ":" in bid:
                                bid = bid.split(":", 1)[1]
                            bark_ids.append(bid)
                    logging.warning(f"⚠️ 错误通知 | 准备向{len(bark_ids)}个设备并行发送Bark通知")
                    
                    # 定义单个设备的通知发送异步函数
                    async def send_bark_notification(session, bark_id):
                        try:
                            # 所有内容进行URL编码处理
                            encoded_title = urllib.parse.quote(bark_main_title)
                            encoded_subtitle = urllib.parse.quote(bark_subtitle)
                            encoded_body = urllib.parse.quote(bark_body)
                            
                            # 添加level=critical和volume=10参数，增加通知的优先级和音量
                            bark_url = f"https://api.day.app/{bark_id}/{encoded_title}/{encoded_subtitle}/{encoded_body}?call=1&level=critical&volume=10"
                            
                            async with session.get(bark_url, timeout=5) as response:
                                if response.status == 200:
                                    return True
                                else:
                                    logging.error(f"Bark通知发送失败(ID: {bark_id}): 状态码 {response.status}")
                                    return False
                        except Exception as e:
                            logging.error(f"Bark通知发送异常(ID: {bark_id}): {str(e)}")
                            return False
                    
                    # 使用aiohttp客户端会话并行发送所有通知
                    async with aiohttp.ClientSession() as session:
                        # 并行发送所有Bark通知
                        results = await asyncio.gather(
                            *[send_bark_notification(session, bark_id) for bark_id in bark_ids],
                            return_exceptions=False
                        )
                        
                        # 计算成功数量
                        success_count = sum(1 for result in results if result is True)
                    
                    if success_count > 0:
                        logging.warning(f"⚠️ 错误通知 | 🔔 Bark响铃通知发送成功: {success_count}/{len(bark_ids)}")
                    else:
                        logging.error("所有Bark通知发送失败")
                else:
                    logging.warning("⚠️ 错误通知 | ⚠️ 未配置BARK_API_ID环境变量，跳过Bark通知")
            except Exception as e:
                logging.error(f"Bark通知过程中出现未预期错误: {str(e)}", exc_info=True)

            # 发送电话呼叫
            try:
                phone_numbers = PHONE_CALL_RECIPIENTS
                if phone_numbers and PHONE_CALL_API_URL and PHONE_CALL_ENCRYPTION_KEY:
                    # 构造电话通知内容
                    call_message = f"线上事故警报，{error_type_display}，请立即检查系统"
                    
                    # 处理多个电话号码（以逗号分隔）
                    # 如果电话号码包含冒号(:)，取冒号后面的部分
                    phones = []
                    for phone in phone_numbers.split(","):
                        phone = phone.strip()
                        if phone:
                            if ":" in phone:
                                phone = phone.split(":", 1)[1]
                            phones.append(phone)
                    if phones:
                        logging.warning(f"⚠️ 错误通知 | ☎️ 准备向{len(phones)}个电话号码发起呼叫")
                        
                        success_count = 0
                        for phone in phones:
                            if make_call(phone, call_message, PHONE_CALL_API_URL, PHONE_CALL_ENCRYPTION_KEY):
                                success_count += 1
                        
                        if success_count > 0:
                            logging.warning(f"⚠️ 错误通知 | ☎️ 电话呼叫发送成功: {success_count}/{len(phones)}")
                        else:
                            logging.error("所有电话呼叫发送失败")
                    else:
                        logging.warning("⚠️ 错误通知 | ⚠️ 未配置有效的电话号码，跳过电话呼叫")
                else:
                    if not PHONE_CALL_RECIPIENTS:
                        logging.warning("⚠️ 错误通知 | ⚠️ 未配置PHONE_CALL_RECIPIENTS环境变量，跳过电话呼叫")
                    if not PHONE_CALL_API_URL:
                        logging.warning("⚠️ 错误通知 | ⚠️ 未配置PHONE_CALL_API_URL环境变量，跳过电话呼叫")
                    if not PHONE_CALL_ENCRYPTION_KEY:
                        logging.warning("⚠️ 错误通知 | ⚠️ 未配置PHONE_CALL_ENCRYPTION_KEY环境变量，跳过电话呼叫")
            except Exception as e:
                logging.error(f"电话呼叫过程中出现未预期错误: {str(e)}", exc_info=True)

            # 将Redis操作放入单独的try-except块中
            try:
                # 更新发送时间并重置计数
                await redis_client.set(last_sent_key, current_time)
                await redis_client.set(count_key, 0)
                await redis_client.set(first_error_key, 0)
                logging.warning("⚠️ 错误通知 | 🔄 重置计数器状态完成")
            except Exception as e:
                logging.error(f"Redis操作失败: {str(e)}", exc_info=True)
                logging.warning("⚠️ 错误通知 | ❗ 无法重置计数器状态，可能导致短时间内重复通知")

    except Exception as ex:
        logging.error(f"发送邮件失败: {str(ex)}", exc_info=True)
        pass
