import uuid
import asyncio
import json
import re
from typing import Optional
from skywalking.decorators import trace
from loguru import logger as logging

from langchain.schema import (
    BaseMessage,
    AIMessage,
    HumanMessage,
    SystemMessage,
    get_buffer_string,
)
from langchain.output_parsers.json import parse_json_markdown

from mygpt.agent_exp.core.chat_model_base import ChatOpenAI
from mygpt.enums import MessagePageInfo, OpenAIModel
from mygpt.error import OperationFailedException
from mygpt.models import Robot, Session, SessionUser, SessionMessage
from mygpt.openai_utils import get_chat_model
from mygpt.session_metrics import (
    create_or_update_session_user_count,
    create_session_message_page_access_metrics,
    create_or_update_session_message_count,
)


@trace()
async def save_message_question(
    ai_obj: Robot,
    session_id: str,
    user_id: str,
    anonymous_username: str,
    question: str,
    message_id: uuid.UUID,
    page_info: Optional[dict] = None,
    created_ip: str = "",
    created_by: str = "",
    is_test: bool = False,
    user_identifier: str = "",
):
    # 记录页面访问信息
    if page_info:
        url = page_info.get("url")
        title = page_info.get("title")
        source = (
            page_info.get("source")
            if page_info.get("source")
            else MessagePageInfo.API.value
        )
    else:
        url = ""
        title = ""
        source = MessagePageInfo.API.value
    # create session if not exist
    session_obj = await Session.get_or_none(session_id=session_id, robot_id=ai_obj.id)
    if not session_obj:
        # 判断是否是新用户
        is_new_user_flag = False
        if created_by:
            is_new_user_flag = await Session.filter(
                robot_id=ai_obj.id, created_by=created_by
            ).exists()
        elif created_ip:
            is_new_user_flag = await Session.filter(
                robot_id=ai_obj.id, created_ip=created_ip
            ).exists()
        else:
            is_new_user_flag = True

        users = [created_by if created_by else created_ip]

        new_users = users if is_new_user_flag else []
        old_users = [] if is_new_user_flag else users

        session_obj = await Session.create(
            session_id=session_id,
            robot=ai_obj,
            title="",
            created_by=created_by,
            created_ip=created_ip,
            is_test=is_test,
            source=source,
            user_identifier=user_identifier,
        )
        if not is_test:
            # 统计创建session的用户数
            asyncio.create_task(
                create_or_update_session_user_count(
                    ai_obj.id, new_users=new_users, old_users=old_users
                )
            )
    else:
        # session 已存在，检查是否需要更新 user_identifier
        if user_identifier and not session_obj.user_identifier:
            session_obj.user_identifier = user_identifier
            await session_obj.save()
    # add to sessionuser
    await SessionUser.add_if_not_exist(session_id, user_id, anonymous_username)

    # add to sessionmessage
    session_message_exist = await SessionMessage.exists(
        id=message_id,
    )
    if session_message_exist:
        raise OperationFailedException("Message already exists")

    question_obj = await session_obj.add_message(
        user_id,
        anonymous_username,
        question,
        message_id,
        is_test=is_test,
        title=title,
        url=url,
    )

    # 统计session的消息数
    tasks = [
        create_session_message_page_access_metrics(ai_obj.id, url, title, 1, source)
    ]
    if not is_test:
        tasks.append(create_or_update_session_message_count(ai_obj.id, 1))
    asyncio.gather(*tasks)

    return question_obj


@trace()
async def fix_json_with_llm(broken_json_str: str, error_message: str):
    """使用LLM来修复损坏的JSON"""
    system_prompt = r"""You are a specialized JSON repair assistant. Your sole purpose is to fix malformed or invalid JSON strings.

CAPABILITIES:
1. Identify common JSON syntax errors such as:
   - Missing or unescaped quotation marks
   - Missing commas or colons
   - Trailing commas
   - Unbalanced brackets or braces
   - Invalid escape sequences
   - Invalid property names
   - Duplicate keys

2. Diagnose and explain JSON parsing issues
   - Identify the exact location of errors when possible
   - Explain the nature of each error

3. Repair broken JSON while preserving original structure and content
   - Properly escape special characters in strings
   - Fix structural issues
   - Maintain the original data as closely as possible

STRING ESCAPING RULES:
- Double quotes inside string values MUST be escaped with a backslash: \"
- Backslashes themselves MUST be escaped: \\
- Control characters must be properly escaped: \n, \t, \r
- Unicode characters can be directly included or escaped as \uXXXX

OUTPUT FORMAT:
```json
{
    "fixed_json": {
        // Your entire fixed JSON here with proper escaping
    }
}
```
KEY CONSTRAINTS:
- NEVER add or remove fields unless they're duplicates
- NEVER change the meaning or values of the data
- NEVER add explanatory text inside the JSON output
- ALWAYS return valid, parseable JSON
- ALWAYS maintain the original structure as closely as possible
- ALWAYS wrap your output in the 'fixed_json' field structure shown above"""

    user_prompt = f"""Please repair the following invalid JSON. It failed to parse with the error:
`{error_message}`

BROKEN JSON:
```
{broken_json_str}
```
Ensure all string values with quotes inside them are properly escaped. Pay special attention to any Chinese text containing quotation marks or special characters. Return ONLY the fixed JSON in the specified wrapper format."""

    llm: ChatOpenAI = get_chat_model(
        OpenAIModel.GPT_4_OMNI_2024_11_20, max_tokens=8192, request_timeout=60
    )
    # user_prompt = user_prompt.format(broken_json_str=broken_json_str, error_message=error_message)
    # 获取meta_prompt
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=user_prompt),
    ]
    resp = await llm.ainvoke(messages)

    try:
        content = resp.content
        # 首先尝试直接解析整个响应
        try:
            parsed = json.loads(content)
            if "fixed_json" in parsed:
                return parsed["fixed_json"]
            return parsed
        except json.JSONDecodeError as out_e:
            # 尝试匹配代码块中的JSON
            json_match = re.search(r"```(?:json)?\s*([\s\S]+?)\s*```", content)
            if json_match:
                json_str = json_match.group(1)
                try:
                    parsed = json.loads(json_str)
                    if "fixed_json" in parsed:
                        return parsed["fixed_json"]
                    return parsed
                except json.JSONDecodeError as inner_e:
                    logging.warning(f"解析JSON字符串失败: {inner_e}")

            # 最后尝试查找最外层的大括号
            start = content.find("{")
            end = content.rfind("}") + 1
            if start >= 0 and end > start:
                json_str = content[start:end]
                try:
                    parsed = json.loads(json_str)
                    if "fixed_json" in parsed:
                        return parsed["fixed_json"]
                    return parsed
                except json.JSONDecodeError as inner_e:
                    logging.warning(f"解析JSON字符串失败: {inner_e}")

        # 如果所有尝试都失败，记录错误并尝试其他修复方法
        logging.error(f"无法从LLM响应中提取有效JSON - content: {content}")
        # 如果所有方法都失败，返回更具体的错误
        raise ValueError("无法提取或修复JSON")
    except Exception as e:
        logging.error(f"解析LLM修复的JSON失败: {e}")
        logging.error(f"LLM响应: {resp.content}")
        raise ValueError(f"JSON修复失败: {str(e)}")


async def robust_json_parser(json_str: str):
    """增强型JSON解析器

    该方法提供高容错性的JSON字符串解析功能:
    1. 首先尝试使用langchain的parse_json_markdown进行标准解析
    2. 若解析失败，则调用LLM智能修复JSON格式错误
    3. 支持处理各种常见JSON语法错误（如引号不匹配、缺少逗号等）

    Args:
        json_str: 需要解析的JSON字符串

    Returns:
        解析后的Python对象（字典或列表）

    Raises:
        OperationFailedException: 当所有修复尝试都失败时抛出
    """
    # 检查输入参数
    if not json_str:
        raise ValueError("JSON字符串不能为空")
    if not isinstance(json_str, str):
        raise ValueError(f"预期输入为字符串，实际为 {type(json_str)}")

    # 基本结构预检查
    if not (
        json_str.strip().startswith("{") or json_str.strip().startswith("[")
    ) or not (json_str.strip().endswith("}") or json_str.strip().endswith("]")):
        logging.warning(f"输入的JSON字符串缺少基本结构标记: {json_str[:100]}...")

    # 尝试标准解析
    try:
        data = parse_json_markdown(json_str)
        return data
    except json.JSONDecodeError as e:
        # 具体的JSON解析错误
        logging.warning(
            f"JSON解析错误: {e}, 位置: {e.pos}, 行: {e.lineno}, 列: {e.colno}"
        )
        error_msg = str(e)
    except Exception as e:
        # 其他类型错误
        logging.warning(f"解析JSON字符串失败: {e}")
        error_msg = str(e)

    # 记录原始输入（截断过长的输入）
    logging.info(
        f"尝试修复JSON字符串，错误类型: {error_msg}, 原始输入: {json_str[:200]}..."
    )

    # 尝试LLM修复，增加超时处理
    try:
        # 设置超时限制（60秒）
        data = await asyncio.wait_for(
            fix_json_with_llm(json_str, error_msg), timeout=120
        )
        logging.info("LLM修复JSON成功")
        return data
    except asyncio.TimeoutError:
        logging.error("LLM修复JSON超时")
        raise OperationFailedException("修复JSON字符串超时")
    except Exception as e:
        logging.error(f"修复JSON字符串失败: {e}")
        raise OperationFailedException("修复JSON字符串失败")


if __name__ == "__main__":
    broken_json_str = """{
  "title": "林常乐人物传记：末日重生者的成长与使命",
  "summary": "林常乐，24岁，前医学院学生，在末日灾难中重生的关键人物。出身于科研家庭，父亲林志远为军方科学家（16岁时意外去世），母亲陈雅琳为生物学家（20岁时神秘失踪）。童年虽物质优渥但缺乏陪伴，8岁时救助小狗（小黑）成为转折点。16岁父亲去世后性格转向内向，但凭优异成绩进入医学院。20岁母亲失踪后独自调查真相，22岁因接近高层政府秘密被迫退学。24岁末世爆发时在救援行动中牺牲并获得重生能力。性格特征为表面冷漠内心柔软，具备出色的医疗技能、观察分析能力和生存技能。重生后逐渐从独行者转变为团队领袖，主要目标包括阻止灾难、寻找母亲、揭露政府阴谋。与顾瑶、墨辰等人有重要交集，内心始终存在信任与戒备的矛盾。标志性特征包括左手腕伤疤、母亲留下的银色怀表，以及从"活着比什么都重要"到"活着不只是为了自己"的人生信条转变。",
  "language": "Chinese",
  "topics": [
    "人物传记",
    "末日重生",
    "家庭悬疑",
    "个人成长"
  ],
  "tags": [
    "林常乐",
    "医学生",
    "重生",
    "预知能力",
    "政府阴谋",
    "家庭失散",
    "团队领袖",
    "生存技能",
    "人物转变",
    "医疗技能"
  ],
  "evaluation": {
    "information_coverage": 8.5,
    "missing_key_aspects": [
      "具体的人际关系网络细节",
      "重生能力的具体表现形式",
      "父亲研究项目的具体内容",
      "末日灾难的具体背景"
    ]
  }
}"""

    # error_message = "Expecting ',' delimiter: line 3 column 340 (char 375)"
    #
    # asyncio.run(fix_json_with_llm(broken_json_str, error_message))
    asyncio.run(robust_json_parser(broken_json_str))
