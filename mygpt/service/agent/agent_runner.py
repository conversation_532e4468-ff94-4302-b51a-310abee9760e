import asyncio
import uuid
from typing import List, Optional

from loguru import logger as logging
from langchain_core.messages.base import (
    BaseMessage,
    BaseMessageChunk,
)
from langchain_core.messages import AIMessage
from langchain.callbacks import AsyncIterator<PERSON>allbackHandler
from langchain.callbacks.base import Async<PERSON><PERSON>backHandler

from mygpt.service.common import save_message_question, fix_json_with_llm
from mygpt.agent_functioncall.callback_handler import (
    FCAgentBaseCallbackHandler,
    FCAgentStreamCallbackHandler,
)
from mygpt.agent_functioncall.context_management.context import BaseContext
from mygpt.agent_functioncall.functioncall_agent_engine import AgentFunctionCallEngine
from mygpt.agent_functioncall.schemas.dataset_schemas import DatasetInfo
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.enums import OpenAIModel, StreamingMessageDataFormat
from mygpt.models import Robot, Session
from mygpt.schemata import QuestionIn


async def run_agent(
    ai_obj: Robot,  # 机器人对象
    messages: List[BaseMessage],  # 长时记忆, 即对话历史 (不包括当前对话)
    agent_mode: PromptType,  # 机器人模式 [chat, tts, ...]
    question_in: QuestionIn,  # 当前对话对象
    robot: Robot,  # 机器人对象
    base_model: OpenAIModel = OpenAIModel.default(),  # 使用的基座模型 [claude_35_sonnet, ...]
    session_id: str = "",  # 会话ID
    user_id: str = "",  # 用户ID
    message_id: uuid.UUID = None,  # 消息ID
    question_record_obj: Optional[Session] = None,  # 当前对话记录对象
    storage_callback: Optional[AsyncCallbackHandler] = None,  # 存储回调函数
    stream_callback: Optional[AsyncIteratorCallbackHandler] = None,  # 流式回调函数
    datasets: Optional[List[DatasetInfo]] = None,  # 数据集
    context: Optional[BaseContext] = None,  # 上下文对象
    store_message: bool = False,  # 是否存储消息
    event: asyncio.Event = None,  # 异步事件
    **kwargs,
):
    # 从ChatService.ChatAgentStream中抽取核心逻辑
    # 包括：初始化Agent、调用LLM、处理回调等
    logging.info(
        f"[AgentService] run_agent - robot_id: {robot.id} | robot_name: {robot.name}"
    )
    logging.info(f"[AgentService] run_agent - messages: {messages}")
    logging.info(f"[AgentService] run_agent - prompt_type: {agent_mode}")
    # 整理参数
    max_tokens = question_in.max_tokens
    temperature = float(kwargs.get("temperature", 0))
    request_timeout = int(kwargs.get("request_timeout", 30))
    stream = True
    logging.info(
        f"[AgentService] base_model: {base_model}, max_tokens: {max_tokens}, temperature: {temperature}, request_timeout: {request_timeout}"
    )
    if event is None:
        event = asyncio.Event()
    if not message_id:
        message_id = uuid.uuid4()
    if not max_tokens:
        # 校验max_tokens参数
        if base_model == OpenAIModel.CLAUDE_35_SONNET:
            max_tokens = 8192
        elif (
            base_model == OpenAIModel.CLAUDE_37_SONNET
            or base_model == OpenAIModel.O3_MINI
        ):
            max_tokens = 32768
        else:
            max_tokens = 4096
    if base_model.value.startswith("gemini"):
        max_tokens = 32768
        # gemini模型的一个坑, 如果消息为空的话, gemini会输出类似
        # `, I see you'd like to say "Hello". I'm ready to assistyou with the information contained in the knowledge basse. How may I help you today?`
        # 感觉像是thinking的内容, 但是如果消息列表中有一条消息, 就会正常进行输出, 语言也会跟随用户消息的语言
        # if not messages:
        #     messages.append(AIMessage("hello"))
    if question_record_obj is None:
        # 构建问题记录对象
        if store_message:
            if not session_id or user_id:
                logging.error(
                    f"AgentService - run_agent - session_id and user_id are required when store_message is True",
                    f"robot_id: {robot.id} - question: {question_in.question}",
                )
                raise ValueError(
                    f"AgentService - run_agent - session_id and user_id are required when store_message is True"
                )
        question_record_obj = await save_message_question(
            ai_obj=ai_obj,
            session_id=session_id,
            user_id=user_id,
            anonymous_username="",
            question=question_in.question,
            message_id=message_id,
            user_identifier=question_in.user_identifier or "",
        )
        if context:
            context.question_record_obj = question_record_obj
    if storage_callback is None:
        storage_callback = FCAgentBaseCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            history=messages,
            question_record_obj=question_record_obj,
            event=event,
            store_message=False,
        )
    if stream_callback is None:
        stream_callback = FCAgentStreamCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            format=StreamingMessageDataFormat.JSON_AST,
            question_record_obj=question_record_obj,
            event=event,
        )
    if store_message is not None:
        # 如果store_message参数不为空, 则使用store_message参数
        storage_callback.store_message = store_message
    # 创建AgentFunctionCallEngine
    afc_engine = AgentFunctionCallEngine(
        question_in=question_in,
        robot=robot,
        chat_history=messages,
        event=event,
        stream_callback=stream_callback,
        storage_callback=storage_callback,
        base_model=base_model,
        stream=stream,
        prompt_type=agent_mode,
        question_record_obj=question_record_obj,
        datasets=datasets,
        max_tokens=max_tokens,
        context=context,
        **kwargs,
    )
    if context:
        context.agent = afc_engine
    logging.info(f"[AgentService] create afc_engine - afc_engine: {afc_engine}")
    response = await afc_engine.run()
    logging.info(f"[AgentService]response: {response}")
    return response
