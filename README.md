# Felo AI

main 分支的代码在变动之后会触发 CI/CD 管道，会同时推送到 DockerHub 公开仓库和 AWS ECR 私有仓库，并部署到集群测试环境。

Source code of Felo AI.

- API Document: https://mygpt.felo.me/docs
- Homepage: https://mygpt.felo.me/

## 分支

### CI

以下分支的代码更新后，会自动CI

- main 测试环境
- 202x.wkxx 正式环境

其他分支基本都不会触发 CI，可以做个临时分支使用

## 线上地址

### 管理后台

测试环境：admin-dev.gbase.ai
正式环境：admin.gbase.ai

## 文件夹

mygpt 业务逻辑
test 对应mygpt的测试文件夹
migrations 数据库迁移，由aerich生成

## Tech Stack

- Python 3.10
- Poetry
- FastAPI
- uvicorn
- openai
- tortoise
- langchain
- postgresql
- redis
- celery

## Preparation before project start

1. Install Python 3.10 from the [official website](https://www.python.org/downloads/)
2. Clone this repo, and `cd` to the repo root directory path
3. Install `poetry`, use `curl -sSL https://install.python-poetry.org | python3 -`(See
   Install: https://python-poetry.org/docs/#installation)
4. Run `poetry config virtualenvs.in-project true` and (`poetry env use python` or `poetry env use python3`)
5. Run `poetry shell`
6. 使用 `vscode` open the repo from the root directory path of the repo
7. Create a new terminal in `vscode`, you should see the word `something like (venv) or (mygpt-py3.10)` in front of the
   command
8. Install project required dependencies, use `poetry install`
9. Set environment variables in your runtime environment
10. Make sure the above goes well, you should now be able to run the project directly
11. `poetry export -f requirements.txt -o requirements.txt --without-hashes`
12. 如果有新安装的软件包，使用`poetry add xxx`，就可以更新toml和lock文件

# Run

Make sure you are currently in the `venv` environment created by the above preparations and just run:

```bash
# 下面是后端启动命令
# 在启动前还有其他步骤，确保3个数据库安装完成，变量文件更改完毕，变量设置成功
poetry run uvicorn mygpt.app:app --reload
# 以下是启动后最后两条日志，看到后可以认为是启动成功
# INFO:root:startup continue
# INFO:root:init agent 
```

# Use Poetry

1. Add or update dependency: `poetry add <package_name>`
2. Remove dependency: `poetry remove <package_name>`
3. Export to requirements.txt: `poetry export -f requirements.txt -o requirements.txt --without-hashes`Attention: `After export, you need remove proxy in requirements.txt`
4. update dependencies: `poetry update <package_name>`

# 安装数据库总结

需要安装关系型数据库 PostgreSQL，缓存数据库 Redis，向量数据库 qdrant。步骤在下面

# Database Operations (TortoiseORM with aerich) 数据库操作

在这一步前，需要确保安装好了PostgreSQL，下面是本地搭建docker版本的PostgreSQL数据库的方法

```sh
# 在windows下可以使用Docker Desktop安装
# 在linux下可以使用命令行安装，目前最新版本是17.1，刚发布不建议安装
docker run -d \
    --name mygpt_pg \
    -e POSTGRES_USER=circleo \
    -e POSTGRES_PASSWORD=circleo \
    -e POSTGRES_DB=mygpt \
    -p 5432:5432 \
    --restart always \
    postgres:16.5
```

以下操作需要在项目根目录下执行，执行前请确保已经位于根目录，并且激活了虚拟环境 `poetry shell`
-（首次）初始化 aerich 和数据库：

```bash
aerich init -t mygpt.settings.TORTOISE_ORM
aerich init-db
```

- Make migrations: aerich migrate (创建完DB后环境直接执行这一步即可)
- Upgrade Schema 每次模型更改后（一般是修改mygpt/models.py）生成迁移并更新数据库：

```bash
aerich migrate
# upgrade会将数据更新到数据库中，完成后可以在sql语句中验证
# SELECT count(*) FROM "aerich" 目前的结果是222条数据
aerich upgrade
```

- 拉取代码后，如果发现数据库模型有变化导致报错，需要执行以下命令：

```bash
aerich upgrade
```

### 安装 Redis，否则启动会报错，连接不到6379

```shell
# 因为 redis 没有设置密码，所以需要设置连接为127.0.0.1
docker run -d \
    --name mygpt-redis \
    -p 127.0.0.1:6379:6379 \
    --restart always \
    redis:7.4.1
```

### 安装 qdrant 向量数据库

```shell
# 安装完成后打开 http://localhost:6333 进行验证
docker run -d \
    -p 6333:6333 \
    -p 6334:6334 \
    --name mygpt-qdrant \
    --restart always \
    qdrant/qdrant:v1.12.4
```

# 本地登录系统总结

windows环境可以将其写在环境变量里，linux环境可以写在.bashrc中，前面加上EXPORT

```shell
# 变量相关
AUTHORIZATION_TYPE=jwt
AUTH0_API_AUDIENCE=https://gbase.ai
AUTH0_DOMAIN=auth0.gbase.ai
JWT_HEADER_KID=gbase.ai
DEFAULT_USER_EMAIL=<EMAIL>
DEFAULT_USER_PASSWORD=123456
# 其他相关变量
OPENAI_BASE_URL=https://one.felo.me/v1
OPENAI_API_KEY=sk-****

# 注意，因为是本地，所以在前端需要同步更改文件".env.development"
VITE_API_BASE_URL=http://127.0.0.1:8000 其中127.0.0.1是后端的IP地址

# 在启动后可以看到登录密码，以在console显示密码为准
```

# 重要：变量文件修改!!!

将".env-example"文件更改为".env"，或者找其他同事要一份正常使用的，原先的API_KEY可能会失效

### URL 白名单功能

系统支持通过环境变量 `URL_WHITELIST` 设置 URL 白名单，允许特定域名绕过某些图片下载和上传限制。白名单格式如下：

domain1,max_images,min_image_width,min_image_height,min_image_size,max_image_size:domain2:domain3

说明：

- 域名之间使用冒号 `:` 分隔
- 每个域名后可以跟随参数，参数之间使用逗号 `,` 分隔
- 参数顺序固定为：max_images, min_image_width, min_image_height, min_image_size, max_image_size
- 如果不提供参数，将使用默认的白名单参数值
- 图片大小参数支持表达式，如 `1024 * 1024 * 10`（表示 10MB）

示例：
URL_WHITELIST=faq.ricoh.jp,999,100,100,1024 * 1024 * 10:amazon.com,20,100,100,1024 * 1024 * 20:example.com

#### 域名匹配规则

其他都是按照惯例的域名匹配规则，支持子域名匹配
 **不匹配情况**：

- 白名单中有 `sub.example.com`，但URL是 `example.com`，不匹配。
- 白名单中有 `example.com`，但URL是 `myexample.com`，不匹配。

```

### 其他需要修改的地方

更改.env文件中的PARSER_URL和PARSER_TOKEN，否则不能解析文件

### 多 parser 配置方法

```dotenv
PARSER_URLS=http://sparticle-parser:8000,http://sparticle-parser-1:8000,http://sparticle-parser-2:8000
PARSER_TOKENS=token,token1,token2
```

URL 和 TOKEN 一一对应，数量一样
如果 PARSER_URLS 和 PARSER_TOKENS 合理配置了，单 parser 配置会被忽略

### 其他需要注意的地方

VECTOR_STORAGE_QDRANT_URL DB_HOST 如果是远程的， OPENSEARCH_URL（搜索数据库）的访问注意是不能直连，需要找同事特殊处理
在浏览器中打开VECTOR_STORAGE_QDRANT_URL，比如http://qdrant.felo-sandbox:6333，显示 title: "qdrant - vector search
engine"
在浏览器中打开 OPENSEARCH_URL，显示 name: "opensearch-cluster-master-1"
正常显示即为成功
rabbitmq目前不使用，有报错没关系

### 以本地jwt为例，本地数据库为例，整体运行过程总结

按上面步骤修改好前端，按文档要求打开前端服务 会有显示 "Local:   https://localhost:5174/"
按上面步骤修改好配置、变量、网络
打开docker中的db, redis和qdrant
按文档要求打开后端（poetry run uvicorn mygpt.app:app --reload）
在终端上会显示 init_users email='<EMAIL>', password=..... INFO:     Application startup complete.
浏览器打开https://localhost:5174，会自动跳转到本地登录页面，登录后即可到创建本地聊天机器人页面
其他情况如果有小问题的根据错误提示再进行排查
另外：postgresql、qdrant可以使用本地和远程的，redis只能使用本地的

### 以使用auth0，远程数据库为例，整体运行过程总结

按上面步骤修改好前端，按文档要求打开前端服务 (nx -> web -> serve -> development -> run)
会有显示 "Local:   https://localhost:5174/"
继续修改前端部分，web/.env.development -> VITE_API_BASE_URL=http://localhost:8000，这里的VITE_PORT是5173
运行5173：nx -> web -> serve -> development -> run
按上面步骤修改好配置、变量、网络 需要注意的地方 -> AUTHORIZATION_TYPE=auth0
按文档要求打开后端（poetry run uvicorn mygpt.app:app --reload）
在终端上会显示 not jwt mode, skip init user. ....... Application startup complete.
浏览器打开https://localhost:5174，会自动跳转到远程登录页面，登录后会到一个错误页面，将5173更改为5174，回车
现在连接的就是本地后端，auth0登录，远程数据库

### 使用pycharm进行本地后端debug

edit configuration -> + -> python -> name -> gbase
script和module中，选择module，右边的命令填入 uvicorn
下面紧接的地方填入 mygpt.app:app --reload
working directory填入gbase的项目目录
Paths to ".env" files 选择 .env 的路径
debug启动即可

# Auth0 与本地 JWT 切换

默认使用 auth0.com 的验证服务，如果要使用本地 JWT 服务，修改环境变量：

```bash
AUTHORIZATION_TYPE=jwt

# or

AUTHORIZATION_TYPE=auth0
```

其它本地 JWT 服务相关配置

- JWT_SECRET: 本地 JWT Token头的 kid 字段，固定值，一般不需要修改

```bash
// jwt token 头
JWT_HEADER_KID=gbase.ai
```

- DEFAULT_USER_EMAIL: 由于当前本地jwt模式没有注册功能，可以提供一个默认用户的邮箱来初始化注册

  默认值为 <EMAIL>，密码随机生成

  在第一次启动时，会自动创建一个用户，创建成功后会在控制台打印帐号和密码，如果需要修改，可以修改环境变量：
- DEFAULT_USER_PASSWORD: 默认用户的密码，如果为空，会随机生成一个密码

```bash
DEFAULT_USER_EMAIL=<EMAIL>

DEFAULT_USER_PASSWORD=123456
```

- AUTH0_DOMAIN: auth0.com 的域名，在本地 JWT 模式下，可以随意填写，用于token加密效验

```bash
AUTH0_DOMAIN=auth0.gbase.ai
```

- AUTH0_API_AUDIENCE: auth0.com 的 API Audience，在本地 JWT 模式下，可以随意填写，用于token加密效验

```bash
AUTH0_API_AUDIENCE=https://gbase.ai
```

# Prompt 内容更新

1. 修改在代码中的prompt
2. 运行 `python generate_prompt.py -m` 生成新的内部版本prompt
3. 【可选】运行 `python generate_prompt.py -m inside=False` 生成新的外部版本prompt
4. 通过aerich控制sql脚本的更新/降级 `aerich upgrade` 或者 `aerich downgrade`

> 注意，必须通过脚本更新，不要直接修改数据库
> 直接在代码中更新，不会直接生效在业务逻辑中，需要通过脚本更新

## 安装pre-commit钩子，自动格式化

### Windows系统

```bash
.\setup.bat
```

### Mac/Linux系统

```bash
bash setup.sh
```

### 对所有文件进行格式化，一般不用跑

```bash
pre-commit run --all-files 
```

### 设计

#### 一个bot关联的所有数据集的embedding模型要相同（防止算法复杂化）

#### 数据源可以分为两类 （20241004）

- 本地上传
  从各类操作系统本地上传的普通文件
- 基于某种规则从外部数据源同步

  - 网页, rule: url + is_recursive
  - lark, rule: share url + is_recursive
  - gitbook, ...
  - sitemap, ...
  - gdrive
  - onedrive

### 注意点

#### 解析时注意

同一个文档的所有chunk都有必须有相同的 metadata.doc_id 属性，这个属性是一个uuid，用于标识一个文档的所有chunk的文档的id，召回时会用到。

这个属性错误，会导致最终prompt拼接时发生错误，导致context浪费，回答质量下降

prompt拼接的详细代码查看：mygpt.openai_utils.get_final_chat_context

（这里最好统一改成metadata.file_id，但是为了兼容老数据，没有改）

### FAQ批量导入与导出功能 [mygpt/endpoints/faqs.py](mygpt/endpoints/faqs.py)

#### 用途

1. 方便用户批量导入faq
2. 方便用户批量更新faq（比如批量修改现有faq中某些的文本，并重新导入）

#### 数据范围

某个数据集下的所有未被删除的faq

- 已经支持的字段：faq的 **question**，**answer**，**source_type**(这条faq的来源) 字段
- **尚未支持的数据**：答案不是文字的faq，相似问题，faq的树结构

#### 本功能的操作流程

1. 从数据集导出faq数据到excel，或者下载示例文件
2. 修改导出的数据 (修改方法见下文 excel 字段的含义字段的含义和填写方法)
3. 导入修改后的数据

#### excel 字段的含义和填写方法

1. id：必填，这条 faq 在数据库的 id
   - 新增faq：要新增 faq 条目时 id 必须为 0；如果相同的问题不存在数据库中，则会创建新的faq；如果相同的问题已经存在数据库中，则跳过这条数据
   - 修改faq：在修改数据时先使用 faq 导出功能导出 faq 的 excel，保持其中的 id 变，修改question/answer/source type字段即可
2. question：必填，提问
3. answer：必填，回答
4. source_type：来源类型，可以选填；如果填写，会根据source_type更新。可选值：user_import（用户导入的数据）, user_input（页面上手动输入）,
   conversation（对话记录）；留空或者填写错误这个值会被自动设置为user_import

### FAQ

[FAQ.md](FAQ.md)

### 基本端到端测试

大型的代码改动，本地通过基本端到端测试后才能上到测试环境
[test_api_endpoints.py](tests/test_api_endpoints.py)

1. 确保本地代码已经跑通
2. 本地连接测试环境数据库，运行服务，监听8000端口
3. 在项目根目录下，执行以下命令运行测试：

```bash
python tests/test_api_endpoints.py
```

4. 确保所有测试通过，类似以下输出：

```bash
test_robots_endpoint passed.
test_datasets_endpoint passed.
...
```

### DEV测试环境的协调

    为解决dev环境ci上线测试互相覆盖的问题，之前的main分支太混乱，夹杂不知能否上线的改动，现在release分支已经是清理干净，从release分支创建dev分支作为对应dev环境的发布分支。dev分支从此与release相互独立，比较灵活不对应必须发布的feature，随用随测但要防止互相覆盖。

所以希望上dev环境测试的：

1. 在自己的feature分支改好
2. 合并自己的feature分支到dev分支，如果有冲突解决好冲突
3. 合并后的节点上打test.数字标签，push
4. test.数字标签自动触发ci构建与部署DEV环境

### 备注：

如果navicat连接PostgreSQL有报错，需要修改dll文件，参考 https://stackoverflow.com/questions/74773611/cannot-open-connection-after-connecting-to-postgres-15-using-navicat-premium

### 20250408更新

- 文件解析调整（新版本）

```shell
# gptbasesparticle/gb_gptbase-parser:0.0.467
# 本地启动gbase-parser: 添加docker版本的parser, 启动时增加如下变量，并开放端口8010
# 后端的PARSER_URL变量不再需要
BACKEND_API_URL
WEBSITE_CRAWLER_URL
PARSER_FILE_URL
PARSER_URL
# 上面四个都填入: http://host.docker.internal:8000

ENABLE_MARKITDOWN=true
OPENAI_BASE_URL=https://one.felo.me/v1
OPENAI_API_KEY=sk-rtOsPdICXQkIVU****b3b-4
OPENAI_API_MODEL=gpt-4o
SKIP_AUTH=true
ADVANCED_PARSER_KEY_OPENAI=sk-proj-aze***FyLN4A
ADVANCED_PARSER_KEY_CLAUDE=sk-ant-api03-3***YAA
ADVANCED_PARSER_KEY_GEMINI=AIzi***PIiFGTc
IS_USE_OCR true
```

- 增加opensearch本地化的部署

```shell
# gptbasesparticle/opensearch:baodong-v2.0
# 添加如下变量，并开放9200和9600
DISABLE_SECURITY_PLUGIN=true
network.host=0.0.0.0
discovery.type=single-node
```
